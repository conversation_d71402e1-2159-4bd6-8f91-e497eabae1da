{"name": "cloudflare-workers-openapi", "version": "0.0.1", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types", "lint": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix", "check-types": "tsc --noEmit"}, "dependencies": {"chanfana": "^2.8.2", "hono": "^4.8.10", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "24.1.0", "@repo/eslint-config": "workspace:*", "@types/service-worker-mock": "^2.0.4", "typescript": "^5.8.3", "wrangler": "^4.26.1"}}