{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "main": "index.js", "license": "MIT", "dependencies": {"@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "4.0.0-beta.0", "globals": "^16.3.0", "typescript-eslint": "8.38.0"}, "publishConfig": {"access": "public"}}