import { from<PERSON><PERSON> } from 'chanfana';
import { Env, Hono } from 'hono';
import { cors } from 'hono/cors';

import { OpenAIProxy } from './endpoints/openai-proxy';
import { AnthropicProxy } from './endpoints/anthropic-proxy';
import { DatabaseService } from './services/database';
import type { AppContext } from './types';

// Start a Hono app
const app = new Hono<{ Bindings: Env & { DB: D1Database } }>();

// Enable CORS for API access
app.use('*', cors({
  origin: '*',
  allowHeaders: ['Content-Type', 'Authorization', 'x-api-key', 'anthropic-version', 'x-request-id', 'x-user-id'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
}));

// Initialize database on first request
app.use('*', async (c, next) => {
  const db = new DatabaseService(c.env.DB);
  c.executionCtx.waitUntil(db.initializeDatabase());
  await next();
});

// Setup OpenAPI registry for documentation
const openapi = fromHono(app, {
  docs_url: '/',
});

// OpenAI proxy routes - matches /openai/* and forwards to api.openai.com
app.all('/openai/*', async (c: AppContext) => {
  return OpenAIProxy.handleRequest(c);
});

// Anthropic proxy routes - matches /anthropic/* and forwards to api.anthropic.com  
app.all('/anthropic/*', async (c: AppContext) => {
  return AnthropicProxy.handleRequest(c);
});

// Health check endpoint
app.get('/health', (c) => {
  return c.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Basic stats endpoint (optional)
openapi.get('/stats', {
  tags: ['Analytics'],
  summary: 'Get token usage statistics',
  responses: {
    '200': {
      description: 'Token usage statistics',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              total_tokens: { type: 'number' },
              total_requests: { type: 'number' },
              avg_tokens_per_request: { type: 'number' },
            },
          },
        },
      },
    },
  },
}, async (c: AppContext) => {
  const db = new DatabaseService(c.env.DB);
  const stats = await db.getTransactionStats();
  
  return c.json(stats || {
    total_tokens: 0,
    total_requests: 0,
    avg_tokens_per_request: 0
  });
});

// Export the Hono app
export default app;
