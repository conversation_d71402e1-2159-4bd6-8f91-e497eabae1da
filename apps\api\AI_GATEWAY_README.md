# AI Gateway Implementation

## Overview

This AI Gateway provides a proxy service for OpenAI and Anthropic APIs with automatic token usage tracking. It intercepts API calls, forwards them to the respective providers, and logs token consumption to a D1 database.

## Features

- **Dual Provider Support**: Proxy for both OpenAI and Anthropic APIs
- **Token Tracking**: Automatic extraction and logging of token usage
- **Streaming Support**: Handles both streaming and non-streaming responses
- **Database Logging**: Stores transaction records in Cloudflare D1
- **Analytics**: Built-in endpoint for usage statistics
- **Health Monitoring**: Health check endpoint for service monitoring

## Usage

### OpenAI Proxy

Replace `https://api.openai.com` with your gateway URL + `/openai`:

**Before:**
```bash
curl https://api.openai.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

**After:**
```bash
curl https://your-gateway.workers.dev/openai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -H "x-user-id: user123" \
  -d '{
    "model": "gpt-4", 
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### Anthropic Proxy

Replace `https://api.anthropic.com` with your gateway URL + `/anthropic`:

**Before:**
```bash
curl https://api.anthropic.com/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

**After:**
```bash
curl https://your-gateway.workers.dev/anthropic/v1/messages \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_API_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -H "x-user-id: user123" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "max_tokens": 1024,
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## Tracked Endpoints

### OpenAI
- `/v1/chat/completions` - Chat completions API
- `/v1/completions` - Legacy completions API

### Anthropic
- `/v1/messages` - Messages API

All other endpoints are forwarded without token tracking.

## Analytics

### Get Usage Statistics
```bash
curl https://your-gateway.workers.dev/stats
```

Response:
```json
{
  "total_tokens": 15420,
  "total_requests": 89,
  "avg_tokens_per_request": 173.15
}
```

### Health Check
```bash
curl https://your-gateway.workers.dev/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-31T12:00:00.000Z",
  "version": "1.0.0"
}
```

## SDK Integration

### OpenAI Python SDK
```python
import openai

client = openai.OpenAI(
    api_key="your-openai-key",
    base_url="https://your-gateway.workers.dev/openai/v1",
    default_headers={"x-user-id": "user123"}
)

response = client.chat.completions.create(
    model="gpt-4",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

### Anthropic Python SDK
```python
import anthropic

client = anthropic.Anthropic(
    api_key="your-anthropic-key",
    base_url="https://your-gateway.workers.dev/anthropic",
    default_headers={"x-user-id": "user123"}
)

response = client.messages.create(
    model="claude-3-5-sonnet-20241022",
    max_tokens=1024,
    messages=[{"role": "user", "content": "Hello!"}]
)
```

## Database Schema

The gateway tracks usage in a `transactions` table:

```sql
CREATE TABLE transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  provider TEXT NOT NULL,           -- 'openai' or 'anthropic'
  endpoint TEXT NOT NULL,           -- '/v1/chat/completions', etc.
  tokens_used INTEGER NOT NULL,     -- Total tokens consumed
  request_id TEXT,                  -- Optional request tracking ID
  user_id TEXT,                     -- Optional user identifier
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Development

### Running Locally
```bash
cd apps/api
pnpm run dev
```

### Deployment
```bash
cd apps/api
pnpm run deploy
```

### Database Setup
The database schema is automatically initialized on first request. For manual setup:

```bash
wrangler d1 create ai-gateway-db
wrangler d1 execute ai-gateway-db --file=src/schema.sql
```

## Environment Variables

Configure these in `wrangler.jsonc`:

```json
{
  "vars": {
    "OPENAI_BASE_URL": "https://api.openai.com",
    "ANTHROPIC_BASE_URL": "https://api.anthropic.com"
  }
}
```

## Error Handling

- Failed token extraction doesn't block requests
- Database errors are logged but don't affect API responses
- Upstream API errors are forwarded transparently
- Gateway errors return 500 with JSON error response

## Security Considerations

- All API keys are forwarded directly to providers
- No API key validation or rate limiting (implement as needed)
- CORS enabled for cross-origin requests
- Request/response bodies are not logged
- Only successful responses trigger token counting