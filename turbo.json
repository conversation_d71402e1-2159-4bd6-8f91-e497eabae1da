{"$schema": "https://turbo.build/schema.json", "concurrency": "20", "globalPassThroughEnv": ["GITHUB_TOKEN", "DATABASE_URL", "DO_NOT_TRACK", "NODE_OPTIONS"], "ui": "tui", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"], "env": ["NODE_ENV"]}, "start": {"dependsOn": ["build"], "persistent": true, "cache": false, "outputs": []}, "check-types": {"dependsOn": ["^check-types"]}, "lint": {"cache": false}, "lint:fix": {"cache": false}, "dev": {"dependsOn": ["^build"], "persistent": true, "cache": false, "env": ["DEBUG"]}, "cf-typegen": {"dependsOn": ["^cf-typegen"], "cache": false}, "test": {}}}