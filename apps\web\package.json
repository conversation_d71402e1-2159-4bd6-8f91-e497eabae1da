{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --clearScreen false", "build": "tsc -b && vite build", "lint": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix", "check-types": "tsc --noEmit", "deploy": "pnpm run build && wrangler pages deploy", "cf-typegen": "wrangler types"}, "dependencies": {"@repo/ui": "workspace:*", "@tanstack/react-router": "^1.130.8", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router-devtools": "^1.130.2", "@tanstack/router-plugin": "^1.130.2", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-oxc": "^0.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "npm:rolldown-vite@latest", "wrangler": "^4.26.1"}}