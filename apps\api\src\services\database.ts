import type { AIProvider, TransactionRecord } from '../types';

export class DatabaseService {
  constructor(private db: D1Database) {}

  async logTransaction(
    provider: AIProvider,
    endpoint: string,
    tokensUsed: number,
    requestId?: string,
    userId?: string
  ): Promise<TransactionRecord | null> {
    try {
      const result = await this.db
        .prepare(
          `
          INSERT INTO transactions (provider, endpoint, tokens_used, request_id, user_id)
          VALUES (?, ?, ?, ?, ?)
        `
        )
        .bind(provider, endpoint, tokensUsed, requestId || null, userId || null)
        .run();

      if (result.success) {
        return {
          id: result.meta.last_row_id as number,
          provider,
          endpoint,
          tokens_used: tokensUsed,
          request_id: requestId,
          created_at: new Date().toISOString(),
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to log transaction:', error);
      return null;
    }
  }

  async getTransactionStats(
    provider?: AIProvider,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    total_tokens: number;
    total_requests: number;
    avg_tokens_per_request: number;
  } | null> {
    try {
      let query = `
        SELECT 
          SUM(tokens_used) as total_tokens,
          COUNT(*) as total_requests,
          AVG(tokens_used) as avg_tokens_per_request
        FROM transactions
        WHERE 1=1
      `;
      const bindings: any[] = [];

      if (provider) {
        query += ` AND provider = ?`;
        bindings.push(provider);
      }

      if (startDate) {
        query += ` AND created_at >= ?`;
        bindings.push(startDate.toISOString());
      }

      if (endDate) {
        query += ` AND created_at <= ?`;
        bindings.push(endDate.toISOString());
      }

      const result = await this.db
        .prepare(query)
        .bind(...bindings)
        .first();

      return result as any;
    } catch (error) {
      console.error('Failed to get transaction stats:', error);
      return null;
    }
  }

  async getRecentTransactions(limit: number = 100, provider?: AIProvider): Promise<TransactionRecord[]> {
    try {
      let query = `
        SELECT id, provider, endpoint, tokens_used, request_id, user_id, created_at
        FROM transactions
      `;
      const bindings: any[] = [];

      if (provider) {
        query += ` WHERE provider = ?`;
        bindings.push(provider);
      }

      query += ` ORDER BY created_at DESC LIMIT ?`;
      bindings.push(limit);

      const result = await this.db
        .prepare(query)
        .bind(...bindings)
        .all();

      return result.results as TransactionRecord[];
    } catch (error) {
      console.error('Failed to get recent transactions:', error);
      return [];
    }
  }

  async initializeDatabase(): Promise<boolean> {
    try {
      // Read and execute the schema
      const schemaSQL = `
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          provider TEXT NOT NULL CHECK (provider IN ('openai', 'anthropic')),
          endpoint TEXT NOT NULL,
          tokens_used INTEGER NOT NULL CHECK (tokens_used >= 0),
          request_id TEXT,
          user_id TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE INDEX IF NOT EXISTS idx_transactions_provider_date ON transactions(provider, created_at);
        CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
        CREATE INDEX IF NOT EXISTS idx_transactions_request_id ON transactions(request_id);
      `;

      // Split by semicolon and execute each statement
      const statements = schemaSQL
        .split(';')
        .map((s) => s.trim())
        .filter((s) => s.length > 0);

      for (const statement of statements) {
        await this.db.prepare(statement).run();
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      return false;
    }
  }
}
