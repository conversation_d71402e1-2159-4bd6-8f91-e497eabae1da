name: Lint Check

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Check for remaining lint errors
        run: pnpm lint