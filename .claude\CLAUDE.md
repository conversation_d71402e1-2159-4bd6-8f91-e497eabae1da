# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a Turborepo monorepo with pnpm workspaces containing:

- **apps/api**: Cloudflare Worker API using Hono framework with chanfana for OpenAPI
- **apps/web**: React + Vite frontend application
- **packages/ui**: Shared React component library
- **packages/eslint-config**: Shared ESLint configurations
- **packages/typescript-config**: Shared TypeScript configurations

## Development Commands

### Root Level (Turborepo)
- `pnpm dev` - Start all applications in development mode
- `pnpm build` - Build all applications
- `pnpm lint` - Run linting across all packages
- `pnpm lint:fix` - Fix linting issues across all packages

### API (Cloudflare Worker)
- `cd apps/api && pnpm dev` - Start Wrangler dev server on port 3002
- `cd apps/api && pnpm deploy` - Deploy to Cloudflare Workers
- `cd apps/api && pnpm cf-typegen` - Generate Cloudflare Worker types
- `cd apps/api && pnpm lint` - Run ESLint on TypeScript files
- `cd apps/api && pnpm lint:fix` - Fix ESLint issues automatically
- `cd apps/api && pnpm typecheck` - Run TypeScript type checking without emitting files

### Web App (React + Vite)  
- `cd apps/web && pnpm dev` - Start Vite dev server
- `cd apps/web && pnpm build` - Build for production (runs TypeScript check first)
- `cd apps/web && pnpm preview` - Preview production build
- `cd apps/web && pnpm lint` - Run ESLint on TypeScript files
- `cd apps/web && pnpm lint:fix` - Fix ESLint issues automatically
- `cd apps/web && pnpm typecheck` - Run TypeScript type checking without emitting files

## Architecture Overview

### API Structure
The API is built using:
- **Hono**: Web framework for Cloudflare Workers
- **chanfana**: OpenAPI integration for Hono
- **Zod**: Schema validation and type generation

API endpoints are organized in `apps/api/src/endpoints/` with OpenAPI schemas defined using chanfana decorators. The main entry point (`apps/api/src/index.ts`) registers routes and serves OpenAPI documentation at the root path.

### Frontend Structure
The web app uses:
- **React 18** with TypeScript
- **Vite** for bundling and development
- **@repo/ui** shared component library

### Shared Components
The `@repo/ui` package exports reusable React components like `Counter` and `Header` that are consumed by the web application.

## Key Configuration Files

- `turbo.json`: Defines build pipeline with dependency management
- `pnpm-workspace.yaml`: Workspace configuration for monorepo
- `apps/api/wrangler.jsonc`: Cloudflare Worker configuration
- `prettier.config.js`: Code formatting (120 char width, single quotes, semicolons)

## Type Safety
All packages use TypeScript with strict typing. The API uses Zod schemas for runtime validation that also generate TypeScript types. Shared TypeScript configurations are in `packages/typescript-config/`.

## Testing
No test framework is currently configured. When adding tests, check existing setup in package.json files first.

## Code Formatting Standards

### Import Organization
Imports must be sorted in this order with empty lines between groups:

```typescript
// 1. Node built-ins (if any)
import fs from 'fs';
import path from 'path';

// 2. External packages (alphabetical)
import React from 'react';
import { useState, useEffect } from 'react';
import { z } from 'zod';

// 3. Internal imports with @/ paths (alphabetical)
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// 4. Relative imports (alphabetical)
import { helper } from '../utils/helper';
import { Component } from './component';
```

### TypeScript Style

**Types (Preferred over Interfaces):**
```typescript
// ✅ Good - use type instead of interface
type UserData = {
  id: string;
  name: string;
  email?: string;
};

// ✅ Good - empty object types allowed
type EmptyConfig = {};

// ✅ Good - avoid any, use specific types
type ApiResponse<T> = {
  data: T;
  status: number;
};
```

**Function Parameters:**
```typescript
// ✅ Good - unused params prefixed with _
function handleClick(_event: MouseEvent, data: string) {
  console.log(data);
}

// ✅ Good - return types optional but can be explicit
function getUserId(): string {
  return 'user-123';
}
```

### React Component Style

**Component Structure:**
```typescript
import React from 'react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

type Props = {
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
};

// ✅ Good - export components and constants
export const BUTTON_VARIANTS = {
  primary: 'bg-blue-500',
  secondary: 'bg-gray-500'
} as const;

export function MyComponent({ className, children, disabled }: Props) {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Button disabled={disabled || isLoading}>
        {children}
      </Button>
    </div>
  );
}
```

### Tailwind CSS Class Ordering
Classes should be ordered logically:

```typescript
// ✅ Good - layout, spacing, appearance, interactions
<div className={cn(
  'flex flex-col items-center',  // layout
  'p-4 mx-auto max-w-md',       // spacing & sizing  
  'bg-white border rounded-lg', // appearance
  'hover:shadow-lg transition-shadow', // interactions
  className
)}>
```

### Switch Statements
Always include break statements or explicit fallthrough:

```typescript
// ✅ Good
switch (status) {
  case 'loading':
    return <Spinner />;
  case 'error':
    return <ErrorMessage />;
  case 'success':
    return <SuccessContent />;
  default:
    return null;
}

// ✅ Good - explicit fallthrough
switch (type) {
  case 'admin':
  case 'moderator': // explicit fallthrough
    return hasElevatedPermissions();
  case 'user':
    return hasBasicPermissions();
  default:
    return false;
}
```

### Code Style (Prettier)
- **Line width**: 120 characters maximum
- **Quotes**: Single quotes for strings
- **Semicolons**: Always include semicolons
- **Trailing commas**: Include in multiline structures

```typescript
// ✅ Good formatting
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000,
  retries: 3, // trailing comma
};

const longFunctionCall = someFunction(
  'parameter one',
  'parameter two',
  'parameter three', // trailing comma
);
```

### File Naming
- **TypeScript files**: `.ts` extension
- **React components**: `.tsx` extension  
- **Config files**: Match existing patterns (`.js`, `.json`, `.jsonc`)
- Use kebab-case for file names: `user-profile.tsx`