// OpenAI API Response Types
export interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  prompt_tokens_details?: {
    cached_tokens?: number;
  };
  completion_tokens_details?: {
    reasoning_tokens?: number;
  };
}

export interface OpenAIChatCompletionResponse {
  id: string;
  object: 'chat.completion' | 'chat.completion.chunk';
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message?: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }>;
  usage?: OpenAIUsage;
  system_fingerprint?: string;
}

export interface OpenAICompletionResponse {
  id: string;
  object: 'text_completion';
  created: number;
  model: string;
  choices: Array<{
    text: string;
    index: number;
    logprobs?: any;
    finish_reason: string;
  }>;
  usage?: OpenAIUsage;
}

// Anthropic API Response Types
export interface AnthropicUsage {
  input_tokens: number;
  output_tokens: number;
}

export interface AnthropicMessageResponse {
  id: string;
  type: 'message';
  role: 'assistant';
  content: Array<{
    type: 'text';
    text: string;
  }>;
  model: string;
  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | 'tool_use';
  stop_sequence?: string;
  usage: AnthropicUsage;
}

export interface AnthropicStreamResponse {
  type:
    | 'message_start'
    | 'content_block_start'
    | 'content_block_delta'
    | 'content_block_stop'
    | 'message_delta'
    | 'message_stop';
  message?: Partial<AnthropicMessageResponse>;
  usage?: AnthropicUsage;
  delta?: {
    text?: string;
    stop_reason?: string;
  };
}

// Union types for handling both providers
export type AIResponse = OpenAIChatCompletionResponse | OpenAICompletionResponse | AnthropicMessageResponse;

// Token extraction utility types
export interface ExtractedTokens {
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;
}

// Provider endpoints we intercept for token counting
export const TRACKED_ENDPOINTS = {
  openai: ['/v1/chat/completions', '/v1/completions'],
  anthropic: ['/v1/messages'],
} as const;

export type TrackedEndpoint = (typeof TRACKED_ENDPOINTS)[keyof typeof TRACKED_ENDPOINTS][number];
