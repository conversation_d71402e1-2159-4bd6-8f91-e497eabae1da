import type {
  AIProvider,
  AnthropicMessageResponse,
  AnthropicStreamResponse,
  ExtractedTokens,
  OpenAIChatCompletionResponse,
  OpenAICompletionResponse,
} from '../types/ai-providers';

export class TokenExtractor {
  static extractTokensFromResponse(
    provider: AIProvider,
    response: any,
    isStream: boolean = false
  ): ExtractedTokens | null {
    try {
      if (provider === 'openai') {
        return this.extractOpenAITokens(response, isStream);
      } else if (provider === 'anthropic') {
        return this.extractAnthropicTokens(response, isStream);
      }
    } catch (error) {
      console.error(`Failed to extract tokens for ${provider}:`, error);
    }
    return null;
  }

  private static extractOpenAITokens(
    response: OpenAIChatCompletionResponse | OpenAICompletionResponse,
    isStream: boolean
  ): ExtractedTokens | null {
    if (isStream) {
      // For streaming responses, we need to accumulate tokens from the final chunk
      // This should be the last chunk that contains usage information
      if ('usage' in response && response.usage) {
        return {
          input_tokens: response.usage.prompt_tokens,
          output_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens,
        };
      }
      return null;
    }

    // Non-streaming response
    if ('usage' in response && response.usage) {
      return {
        input_tokens: response.usage.prompt_tokens,
        output_tokens: response.usage.completion_tokens,
        total_tokens: response.usage.total_tokens,
      };
    }

    return null;
  }

  private static extractAnthropicTokens(
    response: AnthropicMessageResponse | AnthropicStreamResponse,
    isStream: boolean
  ): ExtractedTokens | null {
    if (isStream) {
      // For streaming, look for message_stop event with usage
      if ('type' in response && response.type === 'message_stop' && response.usage) {
        return {
          input_tokens: response.usage.input_tokens,
          output_tokens: response.usage.output_tokens,
          total_tokens: response.usage.input_tokens + response.usage.output_tokens,
        };
      }
      return null;
    }

    // Non-streaming response
    if ('usage' in response && response.usage) {
      return {
        input_tokens: response.usage.input_tokens,
        output_tokens: response.usage.output_tokens,
        total_tokens: response.usage.input_tokens + response.usage.output_tokens,
      };
    }

    return null;
  }

  static extractTokensFromStreamingResponse(provider: AIProvider, chunks: string[]): ExtractedTokens | null {
    try {
      if (provider === 'openai') {
        return this.extractOpenAIStreamTokens(chunks);
      } else if (provider === 'anthropic') {
        return this.extractAnthropicStreamTokens(chunks);
      }
    } catch (error) {
      console.error(`Failed to extract streaming tokens for ${provider}:`, error);
    }
    return null;
  }

  private static extractOpenAIStreamTokens(chunks: string[]): ExtractedTokens | null {
    // Look for the final chunk with usage information
    for (let i = chunks.length - 1; i >= 0; i--) {
      try {
        const chunk = chunks[i].replace('data: ', '').trim();
        if (chunk === '[DONE]') continue;

        const parsed = JSON.parse(chunk);
        if (parsed.usage) {
          return {
            input_tokens: parsed.usage.prompt_tokens,
            output_tokens: parsed.usage.completion_tokens,
            total_tokens: parsed.usage.total_tokens,
          };
        }
      } catch (e) {
        // Skip invalid chunks
        continue;
      }
    }
    return null;
  }

  private static extractAnthropicStreamTokens(chunks: string[]): ExtractedTokens | null {
    // Look for message_stop event with usage
    for (let i = chunks.length - 1; i >= 0; i--) {
      try {
        const chunk = chunks[i].replace('data: ', '').trim();
        const parsed = JSON.parse(chunk);

        if (parsed.type === 'message_stop' && parsed.usage) {
          return {
            input_tokens: parsed.usage.input_tokens,
            output_tokens: parsed.usage.output_tokens,
            total_tokens: parsed.usage.input_tokens + parsed.usage.output_tokens,
          };
        }
      } catch (e) {
        // Skip invalid chunks
        continue;
      }
    }
    return null;
  }

  static shouldTrackEndpoint(provider: AIProvider, pathname: string): boolean {
    const endpointsToTrack = {
      openai: ['/v1/chat/completions', '/v1/completions'],
      anthropic: ['/v1/messages'],
    };

    return endpointsToTrack[provider]?.includes(pathname) || false;
  }

  static isStreamingResponse(headers: Headers): boolean {
    const contentType = headers.get('content-type') || '';
    return (
      contentType.includes('text/event-stream') ||
      contentType.includes('application/x-ndjson') ||
      headers.get('transfer-encoding') === 'chunked'
    );
  }
}
