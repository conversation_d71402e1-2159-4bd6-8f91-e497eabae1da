{"name": "@repo/ui", "version": "0.0.0", "exports": {".": "./index.ts", "./counter": "./components/counter.ts", "./header": "./components/header.ts", "./setup-counter": "./utils/counter.ts"}, "license": "MIT", "scripts": {"lint": "eslint \"**/*.{ts,tsx}\"", "lint:fix": "eslint \"**/*.{ts,tsx}\" --fix"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}