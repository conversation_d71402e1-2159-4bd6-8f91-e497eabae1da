import type { AppContext } from '../types';
import { DatabaseService } from '../services/database';
import { TokenExtractor } from '../utils/token-extractor';

export class AnthropicProxy {
  static async handleRequest(c: AppContext): Promise<Response> {
    try {
      const db = new DatabaseService(c.env.DB);
      const url = new URL(c.req.url);
      const pathname = url.pathname.replace('/anthropic', '');
      const baseUrl = c.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com';
      const targetUrl = `${baseUrl}${pathname}${url.search}`;

      // Extract request ID for tracking
      const requestId = c.req.header('x-request-id') || crypto.randomUUID();
      const userId = c.req.header('x-user-id');

      // Forward the request to Anthropic
      const forwardedHeaders = new Headers();
      c.req.header() && Object.entries(c.req.header()).forEach(([key, value]) => {
        // Forward all headers except host and some cloudflare-specific ones
        if (!['host', 'cf-ray', 'cf-connecting-ip'].includes(key.toLowerCase())) {
          forwardedHeaders.set(key, value as string);
        }
      });

      const requestBody = c.req.method !== 'GET' ? await c.req.arrayBuffer() : undefined;

      const response = await fetch(targetUrl, {
        method: c.req.method,
        headers: forwardedHeaders,
        body: requestBody,
      });

      // Clone response for token extraction
      const responseClone = response.clone();
      
      // Check if this endpoint should be tracked for tokens
      const shouldTrack = TokenExtractor.shouldTrackEndpoint('anthropic', pathname);
      const isStreaming = TokenExtractor.isStreamingResponse(response.headers);

      if (shouldTrack && response.ok) {
        // Handle token extraction and logging asynchronously
        c.executionCtx.waitUntil(
          this.extractAndLogTokens(
            responseClone,
            db,
            pathname,
            requestId,
            userId,
            isStreaming
          )
        );
      }

      // Return the original response
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });

    } catch (error) {
      console.error('Anthropic proxy error:', error);
      return new Response(
        JSON.stringify({ error: 'Internal proxy error' }),
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' } 
        }
      );
    }
  }

  private static async extractAndLogTokens(
    response: Response,
    db: DatabaseService,
    endpoint: string,
    requestId: string,
    userId?: string,
    isStreaming: boolean = false
  ): Promise<void> {
    try {
      if (isStreaming) {
        await this.handleStreamingResponse(response, db, endpoint, requestId, userId);
      } else {
        await this.handleNonStreamingResponse(response, db, endpoint, requestId, userId);
      }
    } catch (error) {
      console.error('Failed to extract and log tokens:', error);
    }
  }

  private static async handleNonStreamingResponse(
    response: Response,
    db: DatabaseService,
    endpoint: string,
    requestId: string,
    userId?: string
  ): Promise<void> {
    try {
      const responseData = await response.json();
      const tokens = TokenExtractor.extractTokensFromResponse('anthropic', responseData, false);

      if (tokens) {
        await db.logTransaction(
          'anthropic',
          endpoint,
          tokens.total_tokens,
          requestId,
          userId
        );
      }
    } catch (error) {
      console.error('Failed to handle non-streaming response:', error);
    }
  }

  private static async handleStreamingResponse(
    response: Response,
    db: DatabaseService,
    endpoint: string,
    requestId: string,
    userId?: string
  ): Promise<void> {
    try {
      const reader = response.body?.getReader();
      if (!reader) return;

      const decoder = new TextDecoder();
      const chunks: string[] = [];
      let finalTokens: any = null;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        chunks.push(chunk);

        // Anthropic streaming format: "data: {json}\n\n"
        const lines = chunk.split('\n').filter(line => line.trim().startsWith('data: '));
        
        for (const line of lines) {
          try {
            const data = line.replace('data: ', '').trim();
            if (!data) continue;
            
            const parsed = JSON.parse(data);
            const tokens = TokenExtractor.extractTokensFromResponse('anthropic', parsed, true);
            
            if (tokens) {
              finalTokens = tokens;
            }
          } catch (e) {
            // Skip invalid JSON chunks
            continue;
          }
        }
      }

      // If we found tokens, log them
      if (finalTokens) {
        await db.logTransaction(
          'anthropic',
          endpoint,
          finalTokens.total_tokens,
          requestId,
          userId
        );
      } else {
        // Fallback: try to extract from all chunks
        const tokens = TokenExtractor.extractTokensFromStreamingResponse('anthropic', chunks);
        if (tokens) {
          await db.logTransaction(
            'anthropic',
            endpoint,
            tokens.total_tokens,
            requestId,
            userId
          );
        }
      }

    } catch (error) {
      console.error('Failed to handle streaming response:', error);
    }
  }
}