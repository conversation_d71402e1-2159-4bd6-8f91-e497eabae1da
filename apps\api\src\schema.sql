-- AI Gateway Transaction Tracking Schema

CREATE TABLE IF NOT EXISTS transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  provider TEXT NOT NULL CHECK (provider IN ('openai', 'anthropic')),
  endpoint TEXT NOT NULL,
  tokens_used INTEGER NOT NULL CHECK (tokens_used >= 0),
  request_id TEXT,
  user_id TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Index for efficient querying by provider and date
CREATE INDEX IF NOT EXISTS idx_transactions_provider_date ON transactions(provider, created_at);

-- Index for efficient querying by user_id
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);

-- Index for request_id lookups
CREATE INDEX IF NOT EXISTS idx_transactions_request_id ON transactions(request_id);