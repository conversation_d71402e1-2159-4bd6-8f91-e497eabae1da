import type { Context, Env } from 'hono';
import { z } from 'zod';

export type AppContext = Context<{ Bindings: Env & { DB: D1Database } }>;

export type AIProvider = 'openai' | 'anthropic';

export interface TokenUsage {
  input_tokens?: number;
  output_tokens?: number;
  total_tokens?: number;
}

export interface TransactionRecord {
  id?: number;
  provider: AIProvider;
  endpoint: string;
  tokens_used: number;
  request_id?: string;
  created_at?: string;
}
